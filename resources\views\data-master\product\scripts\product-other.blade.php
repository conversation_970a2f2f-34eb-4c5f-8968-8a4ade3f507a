<script>
    $(document).ready(function() {
        const $distributionType = $('#distribution_type');
        const $responsibleUsers = $('#responsible_users');
        const $valuesContainer = $('#distribution_values_container');
        const $valuesInputs = $('#distribution_values_inputs');
        const $errorsContainer = $('#distribution_errors');

        // Initialize select2 if you're using it
        $responsibleUsers.select2({
            placeholder: "Pilih penanggung jawab",
            width: '100%'
        });

        // Function to update UI based on distribution type
        function updateDistributionUI() {
            const type = $distributionType.val();
            const selectedOptions = $responsibleUsers.find('option:selected');

            // Clear previous inputs and errors
            $valuesInputs.empty();
            $errorsContainer.empty();

            if (type === 'equal' || selectedOptions.length === 0) {
                $valuesContainer.hide();
                return;
            }

            $valuesContainer.show();
            let totalPercentage = 0;

            selectedOptions.each(function() {
                const $option = $(this);
                const userId = $option.val();
                const userName = $option.text().split(' - ')[0];
                const optionType = $option.data('distribution-type') || type;
                let currentValue = $option.data('distribution-value');

                // Set default value if not set
                if (!currentValue || optionType !== type) {
                    currentValue = type === 'percentage' ?
                        (100 / selectedOptions.length).toFixed(2) :
                        '1';
                }

                const inputGroup = $(`
                    <div class="mb-3">
                        <label class="form-label">${userName}</label>
                        <div class="input-group">
                            <input type="number"
                                class="form-control distribution-value"
                                name="distribution_values[${userId}]"
                                value="${currentValue}"
                                min="${type === 'percentage' ? '0.01' : '1'}"
                                max="${type === 'percentage' ? '100' : ''}"
                                step="${type === 'percentage' ? '0.01' : '1'}"
                                required
                                data-user-id="${userId}">
                            <span class="input-group-text">${type === 'percentage' ? '%' : 'order'}</span>
                        </div>
                        <div class="form-text text-muted">
                            Nilai distribusi untuk ${userName}
                        </div>
                    </div>
                `);

                $valuesInputs.append(inputGroup);

                if (type === 'percentage') {
                    totalPercentage += parseFloat(currentValue) || 0;
                }
            });

            // For percentage type, show total percentage
            if (type === 'percentage') {
                const $totalDiv = $(`
                    <div class="alert ${Math.abs(totalPercentage - 100) < 0.01 ? 'alert-success' : 'alert-danger'}">
                        Total Persentase: <strong>${totalPercentage.toFixed(2)}%</strong>
                        ${Math.abs(totalPercentage - 100) < 0.01 ? '' : ' (Harus tepat 100%)'}
                    </div>
                `);
                $valuesInputs.append($totalDiv);
            }
        }

        // Function to validate distribution values before form submission
        function validateDistribution() {
            const type = $distributionType.val();
            $errorsContainer.empty();

            if (type === 'percentage') {
                let total = 0;
                let isValid = true;

                $('.distribution-value').each(function() {
                    const value = parseFloat($(this).val()) || 0;
                    total += value;

                    if (value <= 0) {
                        isValid = false;
                        const userId = $(this).data('user-id');
                        const userName = $responsibleUsers.find(`option[value="${userId}"]`).text().split(' - ')[0];
                        $errorsContainer.append(`<div>Nilai untuk ${userName} harus lebih dari 0%</div>`);
                    }
                });

                if (Math.abs(total - 100) > 0.01) {
                    isValid = false;
                    $errorsContainer.append(`<div>Total persentase harus tepat 100%. Saat ini: ${total.toFixed(2)}%</div>`);
                }

                return isValid;
            }

            return true;
        }

        // Event listeners
        $distributionType.on('change', updateDistributionUI);
        $responsibleUsers.on('change', updateDistributionUI);

        // Realtime validation for percentage inputs with debounce to prevent loops
        let updateTimeout;
        $valuesInputs.on('input', '.distribution-value', function() {
            if ($distributionType.val() === 'percentage') {
                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(function() {
                    updateDistributionUI();
                }, 300); // Debounce for 300ms
            }
        });

        // Form submission validation - attach to window for global access
        window.validateResponsibleUsersDistribution = function() {
            if ($distributionType.val() === 'percentage' && !validateDistribution()) {
                $errorsContainer.append('<div class="mt-2">Silakan perbaiki kesalahan di atas sebelum menyimpan.</div>');
                $valuesContainer.get(0).scrollIntoView({
                    behavior: 'smooth'
                });
                return false;
            }
            return true;
        };

        // Initialize UI
        updateDistributionUI();
    });
</script>
